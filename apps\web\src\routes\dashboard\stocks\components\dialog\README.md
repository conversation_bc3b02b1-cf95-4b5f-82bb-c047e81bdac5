# Stock Management CRUD Dialogs

This directory contains all the dialog components for managing stock items in the application.

## Components

### 1. AddStockDialog (`add-stock-dialog.tsx`)

- **Purpose**: Create individual stock items
- **Features**:
  - Product and variant selection dropdowns
  - Form validation with Zod schema
  - Real-time variant loading based on selected product
  - Integration with `stock.create` tRPC mutation
  - Automatic UUID generation on the server

**Usage**:

```tsx
import { AddStockDialog } from "./add-stock-dialog";

<AddStockDialog onSuccess={() => console.log("Stock created!")} />;
```

### 2. BulkUploadDialog (`bulk-upload-dialog.tsx`)

- **Purpose**: Upload multiple stock items via CSV file
- **Features**:
  - CSV template download
  - File parsing and validation
  - Preview table with error highlighting
  - Product/variant selection for all uploaded items
  - Integration with `stock.createBulk` tRPC mutation

**CSV Format**:

```csv
name,email,password
Premium Account 1,<EMAIL>,password123
Premium Account 2,<EMAIL>,password456
```

**Usage**:

```tsx
import { BulkUploadDialog } from "./bulk-upload-dialog";

<BulkUploadDialog onSuccess={() => console.log("Bulk upload completed!")} />;
```

### 3. EditStockDialog (`edit-stock-dialog.tsx`)

- **Purpose**: Edit existing stock items
- **Features**:
  - Pre-filled form with current stock data
  - Only allows editing of AVAILABLE stocks
  - Form validation with Zod schema
  - Integration with `stock.update` tRPC mutation

**Usage**:

```tsx
import { EditStockDialog } from "./edit-stock-dialog";

<EditStockDialog
  stock={stockItem}
  onSuccess={() => console.log("Stock updated!")}
>
  <Button>Edit Stock</Button>
</EditStockDialog>;
```

### 4. DeleteStockDialog (`delete-stock-dialog.tsx`)

- **Purpose**: Delete stock items with confirmation
- **Features**:
  - Confirmation dialog with stock details
  - Only allows deletion of AVAILABLE stocks
  - Clear warning messages for assigned/sold stocks
  - Integration with `stock.delete` tRPC mutation

**Usage**:

```tsx
import { DeleteStockDialog } from "./delete-stock-dialog";

<DeleteStockDialog
  stock={stockItem}
  onSuccess={() => console.log("Stock deleted!")}
>
  <Button variant="destructive">Delete</Button>
</DeleteStockDialog>;
```

## Business Rules

### Stock Status Permissions

- **AVAILABLE**: Can be edited and deleted
- **ASSIGNED**: Cannot be edited or deleted (read-only)
- **SOLD**: Cannot be edited or deleted (read-only)

### Validation Rules

- **Account Name**: Required, minimum 1 character
- **Email**: Required, must be valid email format
- **Password**: Required, minimum 1 character
- **Product**: Required for new stocks
- **Variant**: Required for new stocks

### Auto-generated Features

- **Stock ID**: Automatically generated UUID (e.g., `550e8400-e29b-41d4-a716-************`)
- **Status**: New stocks default to "AVAILABLE"
- **Timestamps**: Created and updated timestamps are managed automatically

## Integration

All dialogs are integrated with:

- **tRPC**: For API communication
- **React Query**: For caching and invalidation
- **Tanstack Form**: For form management and validation
- **Zod**: For schema validation
- **Sonner**: For toast notifications

## Error Handling

- Form validation errors are displayed inline
- API errors are shown as toast notifications
- File parsing errors are highlighted in the bulk upload preview
- Permission-based actions are disabled with clear messaging

## Accessibility

- All dialogs have proper ARIA labels
- Keyboard navigation is supported
- Screen reader friendly with semantic HTML
- Focus management for dialog interactions

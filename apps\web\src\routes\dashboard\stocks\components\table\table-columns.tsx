import type { ColumnDef, FilterFn } from "@tanstack/react-table";
import { Key, EllipsisIcon, Eye, Package, Edit, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { AppRouter } from "../../../../../../../server/src/routers";
import type { inferRouterOutputs } from "@trpc/server";
import { EditStockDialog } from "../dialog/edit-stock-dialog";
import { DeleteStockDialog } from "../dialog/delete-stock-dialog";

// Types
type RouterOutputs = inferRouterOutputs<AppRouter>;
type StocksResponse = RouterOutputs["stock"]["getAll"];
export type Stock = StocksResponse["stocks"][number];

// Custom filter function for multi-column searching
const multiColumnFilterFn: FilterFn<Stock> = (row, _columnId, filterValue) => {
  const searchableRowContent = `${row.original.name} ${
    row.original.email || ""
  } ${row.original.variant.name} ${
    row.original.variant.product.name
  }`.toLowerCase();
  const searchTerm = (filterValue ?? "").toLowerCase();
  return searchableRowContent.includes(searchTerm);
};

// Status filter function
const statusFilterFn: FilterFn<Stock> = (row, _columnId, filterValue) => {
  if (!filterValue) return true;
  return row.original.status === filterValue;
};

// Row Actions Component
function RowActions({ row }: { row: any }) {
  const stock = row.original as Stock;

  const handleViewDetails = () => {
    // TODO: Implement view details functionality
    console.log("View details for stock:", stock.id);
  };

  const canEdit = stock.status === "AVAILABLE";
  const canDelete = stock.status === "AVAILABLE";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex justify-end">
          <Button
            size="icon"
            variant="ghost"
            className="shadow-none"
            aria-label="Stock actions"
          >
            <EllipsisIcon size={16} aria-hidden="true" />
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuGroup>
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              handleViewDetails();
            }}
            className="cursor-pointer"
          >
            <Eye className="h-4 w-4" />
            <span>View Details</span>
          </DropdownMenuItem>

          <EditStockDialog stock={stock}>
            <DropdownMenuItem
              onSelect={(e) => e.preventDefault()}
              disabled={!canEdit}
              className="cursor-pointer"
            >
              <Edit className="h-4 w-4" />
              <span>Edit</span>
              {!canEdit && (
                <span className="text-xs text-muted-foreground ml-auto">
                  ({stock.status})
                </span>
              )}
            </DropdownMenuItem>
          </EditStockDialog>

          <DeleteStockDialog stock={stock}>
            <DropdownMenuItem
              onSelect={(e) => e.preventDefault()}
              disabled={!canDelete}
              className="cursor-pointer text-destructive focus:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
              <span>Delete</span>
              {!canDelete && (
                <span className="text-xs text-muted-foreground ml-auto">
                  ({stock.status})
                </span>
              )}
            </DropdownMenuItem>
          </DeleteStockDialog>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Status badge component
function StatusBadge({ status }: { status: Stock["status"] }) {
  const statusConfig = {
    AVAILABLE: {
      variant: "default" as const,
      label: "Available",
      className: "bg-green-100 text-green-800 hover:bg-green-100",
    },
    ASSIGNED: {
      variant: "secondary" as const,
      label: "Assigned",
      className: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
    },
    SOLD: {
      variant: "outline" as const,
      label: "Sold",
      className: "bg-gray-100 text-gray-800 hover:bg-gray-100",
    },
  };

  const config = statusConfig[status];

  return (
    <Badge variant={config.variant} className={config.className}>
      {config.label}
    </Badge>
  );
}

// Column Definitions
export const createColumns = (): ColumnDef<Stock>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    size: 28,
    enableSorting: false,
    enableHiding: false,
  },
  {
    header: "Account Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("name")}</div>
    ),
    size: 150,
    filterFn: multiColumnFilterFn,
  },
  {
    header: "Email",
    accessorKey: "email",
    cell: ({ row }) => (
      <div className="font-mono text-sm">{row.getValue("email")}</div>
    ),
    size: 200,
  },
  {
    header: "Product",
    accessorKey: "variant",
    cell: ({ row }) => {
      const variant = row.original.variant;
      return (
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
            <Package className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium">{variant.product.name}</div>
            <div className="text-sm text-muted-foreground">
              {variant.name} - Rp{Number(variant.price).toLocaleString()}
            </div>
          </div>
        </div>
      );
    },
    size: 250,
    enableSorting: false,
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => <StatusBadge status={row.getValue("status")} />,
    size: 120,
    filterFn: statusFilterFn,
  },
  {
    header: "Created",
    accessorKey: "createdAt",
    cell: ({ row }) => {
      const dateValue = row.getValue("createdAt");
      const date = new Date(dateValue as string);
      return <div className="text-sm">{date.toLocaleDateString()}</div>;
    },
    size: 100,
  },
  {
    id: "actions",
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => <RowActions row={row} />,
    size: 60,
    enableHiding: false,
  },
];

// For backward compatibility
export const columns = createColumns();

import { useId, useState } from "react";
import {
  type ColumnFiltersState,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type PaginationState,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { trpc, queryClient } from "@/utils/trpc";
import { useDebounce } from "@/hooks/use-debounce";
import { ErrorState, LoadingState } from "./table-states";
import { createColumns } from "./table-columns";
import { TableContent } from "./table-content";
import { TablePagination } from "./table-pagination";
import { TableToolbar } from "./table-toolbar";

export default function StocksTable() {
  const id = useId();
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "createdAt",
      desc: true,
    },
  ]);

  // Filter states
  const [searchValue, setSearchValue] = useState("");
  const [statusValue, setStatusValue] = useState("");
  const [productValue, setProductValue] = useState("");

  // Create columns
  const columns = createColumns();

  // Debounce the search value to prevent excessive API calls
  const debouncedSearchValue = useDebounce(searchValue, 500);

  // Fetch stocks using tRPC
  const {
    data: stocksData,
    isLoading,
    error,
    refetch,
  } = useQuery(
    trpc.stock.getAll.queryOptions({
      page: pagination.pageIndex + 1,
      limit: pagination.pageSize,
      search: debouncedSearchValue || undefined,
      status: statusValue
        ? (statusValue as "AVAILABLE" | "ASSIGNED" | "SOLD")
        : undefined,
      productId: productValue || undefined,
    })
  );

  const stocks = stocksData?.stocks || [];
  const totalCount = stocksData?.pagination.total || 0;

  const table = useReactTable({
    data: stocks,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    enableSortingRemoval: false,
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pagination.pageSize),
    state: {
      sorting,
      pagination,
      columnFilters,
      columnVisibility,
    },
  });

  if (error) {
    return <ErrorState error={error} onRetry={() => refetch()} />;
  }

  return (
    <div className="space-y-4 w-full max-w-full overflow-hidden">
      <TableToolbar
        table={table}
        id={id}
        disabled={isLoading}
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        statusValue={statusValue}
        onStatusChange={setStatusValue}
        productValue={productValue}
        onProductChange={setProductValue}
      />
      {isLoading ? (
        <LoadingState />
      ) : (
        <>
          <TableContent table={table} />
          <TablePagination table={table} totalCount={totalCount} id={id} />
        </>
      )}
    </div>
  );
}

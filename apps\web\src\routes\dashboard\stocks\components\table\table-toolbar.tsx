import { useRef, useState } from "react";
import type { Table } from "@tanstack/react-table";
import {
  CircleXIcon,
  Columns3Icon,
  ListFilterIcon,
  Trash2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { trpc, trpcClient, queryClient } from "@/utils/trpc";
import type { Stock } from "./table-columns";
import { AddStockDialog } from "../dialog/add-stock-dialog";
import { BulkUploadDialog } from "../dialog/bulk-upload-dialog";

interface TableToolbarProps {
  table: Table<Stock>;
  id: string;
  disabled?: boolean;
  searchValue: string;
  onSearchChange: (value: string) => void;
  statusValue: string;
  onStatusChange: (value: string) => void;
  productValue: string;
  onProductChange: (value: string) => void;
}

export function TableToolbar({
  table,
  id,
  disabled = false,
  searchValue,
  onSearchChange,
  statusValue,
  onStatusChange,
  productValue,
  onProductChange,
}: TableToolbarProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const [bulkDeleteOpen, setBulkDeleteOpen] = useState(false);

  // Fetch products for filtering
  const { data: productsData } = useQuery(
    trpc.product.getAll.queryOptions({
      page: 1,
      limit: 100,
    })
  );

  const bulkDeleteMutation = useMutation({
    mutationFn: async (stockIds: string[]) => {
      return trpcClient.stock.bulkDelete.mutate({
        ids: stockIds,
      });
    },
    onSuccess: (data) => {
      toast.success(
        `${data.deletedCount} stock item(s) deleted successfully!${
          data.skippedCount > 0
            ? ` ${data.skippedCount} item(s) were skipped (sold/assigned).`
            : ""
        }`
      );
      setBulkDeleteOpen(false);
      table.resetRowSelection();
      // Invalidate and refetch stocks query
      queryClient.invalidateQueries({
        queryKey: [["stock", "getAll"]],
      });
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete stock items");
    },
  });

  const handleBulkDelete = () => {
    const selectedRows = table.getSelectedRowModel().rows;
    const stockIds = selectedRows.map((row) => (row.original as Stock).id);
    bulkDeleteMutation.mutate(stockIds);
  };

  const products = productsData?.products || [];

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {table.getSelectedRowModel().rows.length > 0 && (
        <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
          <span className="text-sm text-muted-foreground">
            {table.getSelectedRowModel().rows.length} item(s) selected
          </span>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setBulkDeleteOpen(true)}
            disabled={disabled || bulkDeleteMutation.isPending}
          >
            <Trash2 className="h-4 w-4" />
            Delete Selected
          </Button>
        </div>
      )}

      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="flex flex-wrap items-center gap-3">
          {/* Search filter */}
          <div className="relative">
            <Input
              id={`${id}-input`}
              ref={inputRef}
              className={cn(
                "peer w-full min-w-[200px] sm:min-w-60 ps-9",
                Boolean(searchValue) && "pe-9"
              )}
              value={searchValue}
              onChange={(e) => onSearchChange(e.target.value)}
              placeholder="Search by name or email..."
              type="text"
              aria-label="Search stocks"
              disabled={disabled}
            />
            <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
              <ListFilterIcon size={16} aria-hidden="true" />
            </div>
            {Boolean(searchValue) && (
              <button
                className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
                aria-label="Clear search"
                disabled={disabled}
                onClick={() => {
                  onSearchChange("");
                  if (inputRef.current) {
                    inputRef.current.focus();
                  }
                }}
              >
                <CircleXIcon size={16} aria-hidden="true" />
              </button>
            )}
          </div>

          {/* Product filter */}
          <Select
            value={productValue || "all"}
            onValueChange={(value) =>
              onProductChange(value === "all" ? "" : value)
            }
            disabled={disabled}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Products" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Products</SelectItem>
              {products.map((product) => (
                <SelectItem key={product.id} value={product.id}>
                  {product.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Status filter */}
          <Select
            value={statusValue || "all"}
            onValueChange={(value) =>
              onStatusChange(value === "all" ? "" : value)
            }
            disabled={disabled}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="AVAILABLE">Available</SelectItem>
              <SelectItem value="ASSIGNED">Assigned</SelectItem>
              <SelectItem value="SOLD">Sold</SelectItem>
            </SelectContent>
          </Select>

          {/* Toggle columns visibility */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" disabled={disabled}>
                <Columns3Icon
                  className="-ms-1 opacity-60"
                  size={16}
                  aria-hidden="true"
                />
                View
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                      onSelect={(event) => event.preventDefault()}
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-3">
          <AddStockDialog />
          <BulkUploadDialog />
        </div>
      </div>

      {/* Bulk Delete Confirmation Dialog */}
      <AlertDialog open={bulkDeleteOpen} onOpenChange={setBulkDeleteOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Selected Stock Items</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete{" "}
              {table.getSelectedRowModel().rows.length} selected stock item(s)?
              This action cannot be undone. Only available stocks can be
              deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={bulkDeleteMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              disabled={bulkDeleteMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {bulkDeleteMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
